import 'package:flutter/material.dart';
import 'package:Kairos/pages/accueil/accueil.page.dart';
import 'package:Kairos/pages/dashboard/dashboard.page.dart';
import 'package:Kairos/pages/liste_etablissements/liste_etablissement.page.dart';
import 'package:Kairos/pages/notifications/notifications.page.dart';
import 'package:Kairos/pages/profile/profile.page.dart';
import 'package:Kairos/pages/splashscreen.dart';

void main(){
  runApp(const KairosMobileApp());
}


class KairosMobileApp extends StatelessWidget{
  const KairosMobileApp({super.key});

  @override
  Widget build(BuildContext context){
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: "Kairos Mobile",
      theme: ThemeData(
        primaryColor: Color(0xFF08B3DF),
        colorScheme: ColorScheme.fromSeed(
          seedColor: Colors.blue.shade300,
          secondary: Color(0xFF95A1AA), // Set secondary color
        ),
        buttonTheme: const ButtonThemeData(
          height: 50.0,
          buttonColor: Color(0xFF95A1AA),
        ),
        scaffoldBackgroundColor: Colors.white,
      ),
      home: const Splashscreen(),
      builder: (context, child){
        if(child is Splashscreen || child is AccueilPage){
          return child!;
        } else {
          return SafeArea(child: child!);
          }
      },
      routes: {
        "/accueil": (context) => const AccueilPage(),
        "/liste_etablissement": (context) => const ListeEtablissement(),
        "/dashboard": (context) => const Dashboard(),
        "/profile": (context) => const ProfilePage(),
        "/notifications": (context) => NotificationsPage(),
      }

    );
  }
}

