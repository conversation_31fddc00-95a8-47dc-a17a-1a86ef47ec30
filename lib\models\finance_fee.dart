class FinanceFee {
  final String title;
  final int amount;
  final String status;
  final bool isPaid;
  final bool isObligatory;
  final int? paidAmount;
  final String? paymentDate;
  final String? receiptNumber;

  FinanceFee({
    required this.title,
    required this.amount,
    required this.status,
    required this.isPaid,
    this.isObligatory = false,
    this.paidAmount,
    this.paymentDate,
    this.receiptNumber,
  });
}