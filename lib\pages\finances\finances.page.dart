import 'package:Kairos/widgets/empty_message.widget.dart';
import 'package:flutter/material.dart';
import 'package:Kairos/enums/header_enums.dart';
import 'package:Kairos/models/finance_fee.dart';
import 'package:Kairos/widgets/custom_app_bar.widget.dart';
import 'package:Kairos/pages/finances/finances_widgets/finance_item.widget.dart';
import 'package:Kairos/widgets/custom_spinner.dart';
import 'package:Kairos/widgets/search_bar_sliver.widget.dart';

class FinancesPage extends StatefulWidget{
  final int initialTab;
  const FinancesPage({super.key, this.initialTab = 0});

  @override
  State<FinancesPage> createState() => _FinancesPageState();
}



class _FinancesPageState extends State<FinancesPage> with SingleTickerProviderStateMixin{
  late TabController _tabController;
  late bool _isSearchBarVisible = false;
  bool _isLoading = true;
  final TextEditingController _searchController = TextEditingController();
  late List<FinanceFee> filteredPaidFees = [];
  late List<FinanceFee> filteredUnpaidFees = [];

  // Date filter state
  String? _startDateFilter;
  String? _endDateFilter;

  // Sample data for paid fees
  final List<FinanceFee> paidFees = [
    FinanceFee(
      title: "Droit d'inscription - Octobre",
      amount: 150000,
      status: 'Quittance générée',
      isPaid: true,
      isObligatory: true,
      paidAmount: 100000,
      paymentDate: '09/12/2022',
      receiptNumber: 'Quittance n° QUIT-1-2022-11-3435 du 23/11/2022',
    ),
    FinanceFee(
      title: 'Frais de scolarité - Semestre 1',
      amount: 250000,
      status: 'Quittance générée',
      isPaid: true,
      paidAmount: 250000,
      paymentDate: '15/11/2022',
      receiptNumber: 'Quittance n° QUIT-1-2022-10-2987 du 15/11/2022',
    ),
    FinanceFee(
      title: "Frais d'examen - Semestre 1",
      amount: 50000,
      status: 'Quittance générée',
      isPaid: true,
      paidAmount: 50000,
      paymentDate: '20/01/2023',
      receiptNumber: 'Quittance n° QUIT-1-2023-01-0456 du 20/01/2023',
    ),
  ];

  // Sample data for unpaid fees
  final List<FinanceFee> unpaidFees = [
    FinanceFee(
      title: 'Frais Prépa TOEIC',
      amount: 25000,
      status: 'Aucune quittance générée',
      isPaid: false,
    ),
    FinanceFee(
      title: "Frais d'ouverture de Dossier",
      amount: 15000,
      status: 'Aucune quittance générée',
      isPaid: false,
    ),
    FinanceFee(
      title: 'Frais de scolarité - Semestre 2',
      amount: 250000,
      status: 'Aucune quittance générée',
      isPaid: false,
      isObligatory: true,
    ),
    FinanceFee(
      title: "Frais d'examen - Semestre 2",
      amount: 50000,
      status: 'Aucune quittance générée',
      isPaid: false,
      isObligatory: true,
    ),
    FinanceFee(
      title: 'Frais de bibliothèque',
      amount: 10000,
      status: 'Aucune quittance générée',
      isPaid: false,
    ),
  ];

  @override
  void initState(){
    super.initState();
    filteredPaidFees = List.from(paidFees);
    filteredUnpaidFees = List.from(unpaidFees);
    _tabController = TabController(initialIndex: widget.initialTab, length: 2, vsync: this);
    _searchController.addListener(_filterFinances);

    // Simulate data loading with a delay
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  void dispose(){
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  void _filterFinances() {
    final String query = _searchController.text.toLowerCase();
    final int currentIndex = _tabController.index;
    setState(() {
      if (currentIndex == 0) { // Paid Fees tab
        filteredUnpaidFees = List.from(unpaidFees); // Reset the other list
        filteredPaidFees = paidFees.where((fee) {
          // Text search filter
          bool matchesText = fee.title.toLowerCase().contains(query) ||
              fee.status.toLowerCase().contains(query) ||
              (fee.paymentDate != null && fee.paymentDate!.toLowerCase().contains(query)) ||
              (fee.receiptNumber != null && fee.receiptNumber!.toLowerCase().contains(query));

          // Date range filter (only for paid fees with payment dates)
          bool matchesDateRange = true;
          if (_startDateFilter != null && _endDateFilter != null && fee.paymentDate != null) {
            matchesDateRange = _isDateInRange(fee.paymentDate!, _startDateFilter!, _endDateFilter!);
          }

          return matchesText && matchesDateRange;
        }).toList();
      } else { // Unpaid Fees tab
        filteredPaidFees = List.from(paidFees); // Reset the other list
        filteredUnpaidFees = unpaidFees.where((fee) =>
          fee.title.toLowerCase().contains(query) ||
          fee.status.toLowerCase().contains(query)
        ).toList();
      }
    });
  }

  // Helper method to check if a date is within the specified range
  bool _isDateInRange(String paymentDate, String startDate, String endDate) {
    try {
      // Parse dates - assuming format is dd/MM/yyyy
      DateTime payment = _parseDate(paymentDate);
      DateTime start = _parseDate(startDate);
      DateTime end = _parseDate(endDate);

      return payment.isAfter(start.subtract(const Duration(days: 1))) &&
             payment.isBefore(end.add(const Duration(days: 1)));
    } catch (e) {
      debugPrint("Error parsing dates: $e");
      return true; // If parsing fails, include the item
    }
  }

  // Helper method to parse date string
  DateTime _parseDate(String dateStr) {
    // Handle different date formats
    if (dateStr.contains('/')) {
      // Format: dd/MM/yyyy
      List<String> parts = dateStr.split('/');
      if (parts.length == 3) {
        return DateTime(int.parse(parts[2]), int.parse(parts[1]), int.parse(parts[0]));
      }
    } else if (dateStr.contains('-')) {
      // Format: yyyy-MM-dd
      return DateTime.parse(dateStr);
    }
    throw FormatException("Unable to parse date: $dateStr");
  }

  // Method to handle date filter changes
  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    // Apply the filter immediately
    _filterFinances();
  }

  // Method to clear date filters
  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Reapply filters without date constraint
    _filterFinances();
  }

  @override
  Widget build(BuildContext context){
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(pageSection: HeaderEnum.finances, title: "SITUATION FINANCIÈRE",
            isSearchBarVisible: _isSearchBarVisible,
            onSearchTap: () {
              setState(() {
                _isSearchBarVisible = !_isSearchBarVisible;
              });
            },
          ),
          SliverToBoxAdapter(
            child:
          TabBar(
            controller: _tabController,
            indicatorColor: Colors.amber,
            indicatorSize: TabBarIndicatorSize.tab,
            tabs: [
              Tab(text: "FRAIS PAYÉS"),
              Tab(text: "FRAIS IMPAYÉS"),
            ],
          ),
      ),
          SliverPersistentHeader(
            pinned: true,
            delegate: SearchBarSliver(
            hintText: _tabController.index == 0? "Rechercher frais payés": _tabController.index == 1? "Rechercher frais impayés": "Rechercher ...",
            extentHeight: _isSearchBarVisible? 60.0 : 0.0,
            searchController: _searchController,
            onSearchChanged: (query) => _filterFinances(),
            onDateFilterChanged: _onDateFilterChanged,
            onClearDateFilter: _clearDateFilter,
            hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
          ),),
        SliverFillRemaining(
          child: _isLoading? Center(
              child: CustomSpinner(
                size: 60.0,
                strokeWidth: 5.0,
              ),
            )
           : TabBarView(
            controller: _tabController,
            children: [
              // Paid fees tab
              if(filteredPaidFees.isNotEmpty) ListView.builder(
                padding: EdgeInsets.only(top: 2.0, bottom: 2.0),
                itemCount: filteredPaidFees.length,
                itemBuilder: (context, index) {
                  final fee = filteredPaidFees[index];
                  return FinanceItem(
                    title: fee.title,
                    amount: fee.amount,
                    status: fee.status,
                    isPaid: fee.isPaid,
                    isObligatory: fee.isObligatory,
                    paidAmount: fee.paidAmount,
                    paymentDate: fee.paymentDate,
                    receiptNumber: fee.receiptNumber,
                  );
                },
              ) else Center(child: EmptyMessage(message: "Aucun frais payé trouvé")),

              // Unpaid fees tab
             if(filteredUnpaidFees.isNotEmpty) ListView.builder(
                padding: EdgeInsets.only(top: 16.0, bottom: 16.0),
                itemCount: filteredUnpaidFees.length,
                itemBuilder: (context, index) {
                  final fee = filteredUnpaidFees[index];
                  return FinanceItem(
                    title: fee.title,
                    amount: fee.amount,
                    status: fee.status,
                    isPaid: fee.isPaid,
                    isObligatory: fee.isObligatory,
                  );
                },
              ) else Center(child: EmptyMessage(message: "Aucun frais impayé trouvé")),
            ],
          ),
        )
      ],
    ),
    );
}
}