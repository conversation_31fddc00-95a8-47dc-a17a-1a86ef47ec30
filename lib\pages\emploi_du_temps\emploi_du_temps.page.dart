import 'package:flutter/material.dart';
import 'package:Kairos/enums/header_enums.dart';
import 'package:Kairos/pages/emploi_du_temps/schedule_item.widget.dart';
import 'package:Kairos/widgets/custom_app_bar.widget.dart';
import 'package:Kairos/models/schedule_entry.dart';
import 'package:Kairos/widgets/custom_spinner.dart';
import 'package:Kairos/widgets/search_bar_sliver.widget.dart';


class EmploiDuTempsPage extends StatefulWidget {
  const EmploiDuTempsPage({super.key});

  @override
  State<EmploiDuTempsPage> createState() => _EmploiDuTempsPageState();
}

class _EmploiDuTempsPageState extends State<EmploiDuTempsPage> with TickerProviderStateMixin {
  bool _isLoading = true;
  bool _isSearchBarVisible = false;
  late TextEditingController _searchController;
  late Map<String, List<ScheduleEntry>> _filteredScheduleEntriesGroupedByDay;
  late AnimationController _searchAnimationController;

  final Map<String, List<ScheduleEntry>> scheduleEntriesGroupedByDay = {
    'LUN': [
      ScheduleEntry(
        day: 'LUN',
        date: '12/12',
        timeRange: '08:00 - 10:30',
        courseCode: '1COM177',
        teacher: 'Avec Thiate BARRY',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_2_1erETAGE',
        indicatorType: 'PL',
        indicatorColor: Colors.blue,
      ),
      ScheduleEntry(
        day: 'LUN',
        date: '12/12',
        timeRange: '11:00 - 01:00',
        courseCode: 'ALTC3S2N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM2',
        location: 'BAT_A_13_4eEtage',
        indicatorType: 'PE',
        indicatorColor: Colors.orange,
      ),
      ScheduleEntry(
        day: 'LUN',
        date: '12/12',
        timeRange: '01:30 - 03:30',
        courseCode: 'ALTC3S2N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM2',
        location: 'BAT_A_25_6eEtage',
        indicatorType: 'PT',
        indicatorColor: Colors.green,
      ),
    ],
    'MAR': [
      ScheduleEntry(
        day: 'MAR',
        date: '13/12',
        timeRange: '09:30 - 12:00',
        courseCode: 'ALTC3S2N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM2',
        location: 'BAT_A_14_4eEtage',
        indicatorType: 'PE',
        indicatorColor: Colors.orange,
      ),
      ScheduleEntry(
        day: 'MAR',
        date: '13/12',
        timeRange: '12:00 - 12:30',
        courseCode: '1COM177',
        teacher: 'Avec Thiate BARRY',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_14_4eEtage',
        indicatorType: 'PT',
        indicatorColor: Colors.green,
      ),
    ],
    'MER': [
      ScheduleEntry(
        day: 'MER',
        date: '14/12',
        timeRange: '08:00 - 10:30',
        courseCode: 'ALTC2S1N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_18_5eEtage',
        indicatorType: 'PT',
        indicatorColor: Colors.green,
      ),
      ScheduleEntry(
        day: 'MER',
        date: '14/12',
        timeRange: '11:00 - 12:30',
        courseCode: 'ALTC2S1N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_12_3eETAGE',
        indicatorType: 'PL',
        indicatorColor: Colors.blue,
      ),
    ],
    'JEU': [
      ScheduleEntry(
        day: 'JEU',
        date: '15/12',
        timeRange: '07:00 - 09:30',
        courseCode: 'ALTC3S1N1',
        teacher: 'Avec Ismael NDIAYE',
        courseDetails: 'PALT_C1B_2021 | SEM1',
        location: 'BAT_A_13_4eEtage',
        indicatorType: 'PE',
        indicatorColor: Colors.orange,
      ),
    ],
    'VEN': [
      ScheduleEntry(
        day: 'VEN',
        date: '16/12',
        timeRange: '08:00 - 10:00',
        courseCode: 'ALTC3S2N1',
        teacher: 'Avec Malick Y. FALL',
        courseDetails: 'PALT_C1B_2021 | SEM2',
        location: 'BAT_A_13_4eEtage',
        indicatorType: 'PL',
        indicatorColor: Colors.blue,
      ),
    ],
  };

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredScheduleEntriesGroupedByDay = Map.from(scheduleEntriesGroupedByDay);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );
    _searchController.addListener(_filterScheduleEntries);

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _filterScheduleEntries() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      if (query.isEmpty) {
        _filteredScheduleEntriesGroupedByDay = Map.from(scheduleEntriesGroupedByDay);
      } else {
        _filteredScheduleEntriesGroupedByDay = {};
        scheduleEntriesGroupedByDay.forEach((day, entries) {
          final filteredEntries = entries.where((entry) {
            return entry.day.toLowerCase().contains(query) ||
                   entry.date.toLowerCase().contains(query) ||
                   entry.timeRange.toLowerCase().contains(query) ||
                   entry.courseCode.toLowerCase().contains(query) ||
                   entry.teacher.toLowerCase().contains(query) ||
                   entry.courseDetails.toLowerCase().contains(query) ||
                   entry.location.toLowerCase().contains(query);
          }).toList();
          if (filteredEntries.isNotEmpty) {
            _filteredScheduleEntriesGroupedByDay[day] = filteredEntries;
          }
        });
      }
    });
  }

  void _toggleSearchBarVisibility() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (_isSearchBarVisible) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
        _searchController.clear();
        _filteredScheduleEntriesGroupedByDay = Map.from(scheduleEntriesGroupedByDay);
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: false,
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
            pageSection: HeaderEnum.planning,
            isSearchBarVisible: _isSearchBarVisible,
            title: "EMPLOI DU TEMPS",
            onSearchTap: _toggleSearchBarVisibility,
          ),
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0, // Animate height
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterScheduleEntries(),
                  hintText: "Rechercher un cours...",
                ),
              );
            },),
          _isLoading
          ? SliverFillRemaining(
              child: Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              ),
            )
          : SliverList(
              delegate: SliverChildBuilderDelegate(
                (context, index) {
                  final dayEntries = _filteredScheduleEntriesGroupedByDay.entries.elementAt(index);
                  final day = dayEntries.key;
                  final entries = dayEntries.value;
                  final date = entries.isNotEmpty ? entries.first.date : '';

                  return Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16.0),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          width: 60,
                          margin: const EdgeInsets.only(right: 16.0),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                day,
                                style: const TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              Text(
                                date,
                                style: const TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey,
                                ),
                              ),
                          ],
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: entries.map((entry) => ScheduleItem(scheduleEntry: entry)).toList(),
                          ),
                        ),
                      ],
                    ),
                  );
                },
                childCount: _filteredScheduleEntriesGroupedByDay.length,
              ),
            ),
        ]
      )
    );
  }
}
