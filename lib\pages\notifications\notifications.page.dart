import 'package:flutter/material.dart';
import 'package:Kairos/models/notification_item.dart';
import 'package:Kairos/widgets/custom_app_bar.widget.dart';
import 'package:Kairos/pages/notifications/notifications_widgets/notification_tile.widget.dart';

class NotificationsPage extends StatelessWidget {
  NotificationsPage({super.key});

  final List<NotificationItem> notifications = [
    NotificationItem(
      title: 'Code de validation',
      content: 'Votre pin de réinitialisation de mot de passe: 5691.',
      date: DateTime(2022, 12, 21, 0, 0),
      type: NotificationType.codeValidation,
      sender: '<EMAIL>',
    ),
    NotificationItem(
      title: 'Code de validation',
      content: 'Votre pin de réinitialisation de mot de passe: 1417.',
      date: DateTime(2022, 12, 21, 0, 0),
      type: NotificationType.codeValidation,
      sender: '<EMAIL>',
    ),
    NotificationItem(
      title: 'Code de validation',
      content: 'Votre pin de réinitialisation de mot de passe: 8735.',
      date: DateTime(2022, 12, 21, 0, 0),
      type: NotificationType.codeValidation,
      sender: '<EMAIL>',
    ),
    NotificationItem(
      title: 'test',
      content: 'salut Abdoulaye Test test',
      date: DateTime(2022, 12, 21, 0, 0),
      type: NotificationType.message,
      sender: '<EMAIL>',
    ),
    NotificationItem(
      title: 'test test',
      content: 'Adama Danfa',
      date: DateTime(2022, 12, 21, 0, 0),
      type: NotificationType.message,
      sender: '<EMAIL>',
    ),
  ];

  String formatDate(DateTime date) {
    return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
              isSearchBarVisible: false,
              onSearchTap: null,
              title: "NOTIFICATIONS",
            ),
            SliverToBoxAdapter(
              child: SizedBox(height: 20),
            ),
          SliverList(
            delegate: SliverChildBuilderDelegate(
              (context, index) {
                final notification = notifications[index];
                final formattedDate = formatDate(notification.date);
                return NotificationTile(notification: notification, formattedDate: formattedDate);
              },
              childCount: notifications.length,
            ),
          ),
        ],
      ),
    );
  }
}
