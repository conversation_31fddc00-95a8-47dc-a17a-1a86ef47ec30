

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:url_launcher/url_launcher.dart';

class HomeSlideWidget extends StatefulWidget{
  const HomeSlideWidget({super.key, required this.pageController});
  final PageController pageController;

  @override
  State<HomeSlideWidget> createState() => _HomeSlideWidgetState();
}

class _HomeSlideWidgetState extends State<HomeSlideWidget>{
  double _opacity = 0.0;

  @override
  void initState(){
    super.initState();
    Future.delayed(Duration(milliseconds: 1000),(){
      setState(() {
        _opacity = 1.0;
      });
    });
  }

  @override
  Widget build(BuildContext context){
    return Column(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: [
            Text("BIENVENUE SUR ", style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold, color: Theme.of(context).primaryColor)),
            SvgPicture.asset("assets/images/logo_kairos.svg"),
            SizedBox(height: 20, 
                    width: 200, 
                    child: Divider(color: Theme.of(context).primaryColor, thickness: 5,),),
            Spacer(),
            SvgPicture.asset("assets/images/bienvenue.svg"),
            Spacer(),
            SizedBox(height: 20),
            Spacer(),
            FilledButton(
              style: ButtonStyle(
                  backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                  fixedSize: WidgetStateProperty.all(Size(300, 50))),
              onPressed: () {
                debugPrint('the user didAcceptPolicy AND clicked on `Continue` button');
                widget.pageController.nextPage(duration: Duration(milliseconds: 500), curve: Curves.easeInOut);
              },
              child: Text("CONTINUER", style: TextStyle(fontWeight: FontWeight.bold), ),
              ),
              SizedBox(height: 10,),
              Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20.0),
              // child: Text("En cliquant sur `Continuer`, vous acceptez notre politique de confidentialité", 
              child: Text.rich(textAlign: TextAlign.center, TextSpan(
                children: [
                  TextSpan(text: "En cliquant sur "),
                  TextSpan(text: "CONTINUER", style: TextStyle(fontWeight: FontWeight.bold)),
                  TextSpan(text: ", vous acceptez notre"),
                  TextSpan(text: " politique de confidentialité", 
                          recognizer: TapGestureRecognizer()..onTap = () async {
                            final Uri url = Uri.parse("https://www.sensoft.sn/kairos/");
                            if(await canLaunchUrl(url)){
                              await launchUrl(url);
                            } else {
                              throw "Could not launch $url";
                              }
                          },
                          style: TextStyle(color: Theme.of(context).primaryColor)),
                ]
              )),
              // textAlign: TextAlign.center, style: TextStyle(fontSize: 12)),
            ),
            Spacer(),
          
          ],
        );
  }
}