class NotificationItem {
  final String title;
  final String content;
  final DateTime date;
  final NotificationType type;
  final String sender;
  final bool isRead;

  NotificationItem({
    required this.title,
    required this.content,
    required this.date,
    required this.type,
    required this.sender,
    this.isRead = false,
  });
}

enum NotificationType {
  codeValidation,
  message,
  other,
}

// You can add more attributes if needed, e.g., icon, etc.
