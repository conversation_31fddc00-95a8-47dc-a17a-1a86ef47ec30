import 'package:flutter/material.dart';
import 'package:Kairos/pages/accueil/accueil.page.dart';
import 'package:Kairos/widgets/custom_spinner.dart';

class ProfilePage extends StatefulWidget {
  const ProfilePage({super.key});

  @override
  State<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  final String fullName = "Papa Amadou Diallo";
  final String profileType = "ETUDIANT";
  final String userId = "USER12345";
  final String currentSchool = "SENSOFT - BEM";
  final String phoneNumber = "+221774294171";

  bool _isLoading = true;
  @override
  void initState() {
    super.initState();
    Future.delayed(const Duration(seconds: 4), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  void _editProfileImage() {
    debugPrint('Edit profile image button tapped');
  }

  void _goToListSchools(){
    debugPrint("User wants to his list of schools");
    Navigator.pushNamed(context, "/liste_etablissement");
  }

  void _logout() {
    debugPrint('Logout button tapped');
    Navigator.pushReplacement(
      context,
      MaterialPageRoute(builder: (context) => AccueilPage()),
    );
  }

  @override
  Widget build(BuildContext context) {
    const double heroHeight = 200.0;
    const double avatarRadius = 100.0;
    const double avatarTopPosition = heroHeight - avatarRadius;

    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text('Profile'),
        foregroundColor: Colors.white,
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SingleChildScrollView(
            child: Column(
              children: [
                Stack(
                  clipBehavior: Clip.none,
                  children: [
                    Hero(
                      tag: "hero_profile",
                      transitionOnUserGestures: true,
                      child: Image.asset("assets/images/header_dashboard.png", width: MediaQuery.of(context).size.width, fit: BoxFit.cover,),
                    ),
                    Positioned(
                      top: avatarTopPosition,
                      left: MediaQuery.of(context).size.width / 2 - avatarRadius,
                      child: CircleAvatar(
                        radius: avatarRadius,
                        backgroundImage: AssetImage('assets/images/img_user.png'),
                      ),
                    ),

                    Positioned(
                      top: avatarTopPosition+30,
                      left: MediaQuery.of(context).size.width / 2 + avatarRadius - 30,
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black26,
                              blurRadius: 4.0,
                              offset: Offset(0, 2),
                            ),
                          ],
                        ),
                        child: IconButton(
                          icon: const Icon(Icons.camera_alt),
                          color: Colors.blue,
                          onPressed: _editProfileImage,
                        ),
                      ),
                    ),
                  ],
                ),
               Padding(
                  padding: const EdgeInsets.only(top: avatarRadius, left: 16, right: 16),
                  child:  _isLoading
        ? Center(
          heightFactor: 4,
            child: CustomSpinner(
              size: 60.0,
              strokeWidth: 5.0,
            ),
          )
        : Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            ListTile(
                              title: const Text('Full Name', style: TextStyle(color: Colors.black)),
                              subtitle: Text(fullName),
                            ),
                            const Divider(),
                             ListTile(
                              title: const Text('Profile Type'),
                              subtitle: Text(profileType),
                            ),
                            const Divider(),
                             ListTile(
                              title: const Text('User ID'),
                              subtitle: Text(userId),
                            ),
                            const Divider(),
                             ListTile(
                              title: const Text('Current School'),
                              subtitle: Text(currentSchool),
                            ),
                            const Divider(),
                             ListTile(
                              title: const Text('Phone Number'),
                              subtitle: Text(phoneNumber),
                            ),
                            const Divider(),

                            const SizedBox(height: 40.0),

                            Center(
                              child: ElevatedButton(
                                onPressed: _logout,
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.red,
                                  padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 15),
                                  minimumSize: const Size(200, 50),
                                ),
                                child: const Text(
                                  'DÉCONNEXION',
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ),
                            const SizedBox(height: 7.0),
                            Center(child:
                            FilledButton(onPressed: _goToListSchools,
                            style: ButtonStyle(
                              minimumSize: WidgetStateProperty.all(const Size(200, 50)),
                              backgroundColor: WidgetStateProperty.all(Theme.of(context).primaryColor),
                            ),
                            child: const Text("VOIR LISTE ÉTABLISSEMENT")),),
                            const SizedBox(height: 20.0),
                          ],
                        ),
                    ),
              ],
            ),
      ),
    );
  }
}
