import 'package:flutter/material.dart';
import 'package:Kairos/models/schedule_entry.dart';

class ScheduleItem extends StatelessWidget {
  final ScheduleEntry scheduleEntry;

  const ScheduleItem({super.key, required this.scheduleEntry});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          left: BorderSide(
            color: scheduleEntry.indicatorColor,
            width: 4.0, // Thicker left border
          ),
          top: BorderSide(width: .5, color: scheduleEntry.indicatorColor,),
          right: BorderSide(width: .5, color: scheduleEntry.indicatorColor,),
          bottom: BorderSide(width: .5, color: scheduleEntry.indicatorColor,),
        ),
        borderRadius: BorderRadius.circular(4.0), // Optional: add some rounded corners
      ),
      child: Padding(
        padding: const EdgeInsets.all(7.0), // Adjust padding as needed
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Schedule details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        scheduleEntry.timeRange,
                        style: const TextStyle(fontSize: 14),
                      ),
                      Text(
                        scheduleEntry.location,
                        style: const TextStyle(fontSize: 12, color: Colors.grey),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    scheduleEntry.courseCode,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    scheduleEntry.teacher,
                    style: const TextStyle(fontSize: 14),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    scheduleEntry.courseDetails,
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                  ),
                ],
              ),
            ),
            // Far right: Indicator type circle
            Container(
              width: 30,
              height: 30,
              decoration: BoxDecoration(
                color: scheduleEntry.indicatorColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  scheduleEntry.indicatorType,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
