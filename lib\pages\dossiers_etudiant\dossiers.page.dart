import 'package:flutter/material.dart';
import 'package:Kairos/enums/header_enums.dart';
import 'package:Kairos/widgets/custom_app_bar.widget.dart';
import 'package:Kairos/models/dossier.dart';
import 'package:Kairos/pages/dossiers_etudiant/dossier_item.widget.dart';
import 'package:Kairos/widgets/custom_spinner.dart';
import 'package:Kairos/widgets/empty_message.widget.dart';
import 'package:Kairos/widgets/search_bar_sliver.widget.dart';

class DossiersPage extends StatefulWidget {
  const DossiersPage({super.key});
  @override
  State<DossiersPage> createState() => _DossiersPageState();
}

class _DossiersPageState extends State<DossiersPage> with SingleTickerProviderStateMixin {
  bool _isLoading = true;
  bool _isSearchBarVisible = false; // State to control search bar visibility
  final List<Dossier> dossiers = [
    Dossier(
      initials: 'EI',
      title: 'Entretien individuel',
      boldTitle: 'ENTRETIEN INDIVIDUEL',
      description: 'Entretien individuel avec le responsable de la scolarité',
      date: '14 déc. 2022',
    ),
    Dossier(
      initials: 'RÉ',
      title: 'Réclamation',
      boldTitle: 'RECLAMATION',
      description: 'L\'article Code du travail vous accorde un droit de réclamation',
      date: '13 déc. 2022',
    ),
    Dossier(
      initials: 'DM',
      title: 'Dossier médical',
      boldTitle: 'DOSSIER MEDICAL',
      description: 'Les informations médicales portant sur le patient',
      date: '23 nov. 2022',
    ),
    Dossier(
      initials: 'RD',
      title: 'Remise de documents',
      boldTitle: 'REMISE DOC ADMINISTRATIVE',
      description: 'Remise Doc Administrative',
      date: '22 nov. 2022',
    ),
    Dossier(
      initials: 'RR',
      title: 'Renvoi pour absence répétitive',
      boldTitle: 'ABSENCE RÉPÉTITIVE',
      description: 'Absence Tst',
      date: '22 nov. 2022',
    ),
  ];
  late List<Dossier> _filteredDossiers = []; // List to hold filtered dossiers
  late TextEditingController _searchController; // Controller for the search bar
  String? _startDateFilter;
  String? _endDateFilter;

  late AnimationController _searchAnimationController;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredDossiers = List.from(dossiers); // Initialize filtered list with all dossiers
    _searchController.addListener(_filterDossiers);
    _searchAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 100),
    );

    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _searchAnimationController.dispose();
    super.dispose();
  }


  void _filterDossiers() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      _filteredDossiers = dossiers.where((dossier) {
        // Text search filter
        bool matchesText = dossier.title.toLowerCase().contains(query) ||
               dossier.description.toLowerCase().contains(query) ||
               dossier.date.toLowerCase().contains(query);

        // Date range filter
        bool matchesDateRange = true;
        if (_startDateFilter != null && _endDateFilter != null) {
          matchesDateRange = _isDateInRange(dossier.date, _startDateFilter!, _endDateFilter!);
        }

        return matchesText && matchesDateRange;
      }).toList();
    });
  }

  bool _isDateInRange(String dossierDate, String startDate, String endDate) {
    try {
      // Parse dates
      DateTime dossier = _parseDate(dossierDate);
      DateTime start = _parseDate(startDate);
      DateTime end = _parseDate(endDate);

      return dossier.isAfter(start.subtract(const Duration(days: 1))) &&
             dossier.isBefore(end.add(const Duration(days: 1)));
    } catch (e) {
      debugPrint("Error parsing dates: $e");
      return true; // If parsing fails, include the item
    }
  }

  DateTime _parseDate(String dateStr) {
    // Handle different date formats
    if (dateStr.contains('/')) {
      // Format: dd/MM/yyyy
      List<String> parts = dateStr.split('/');
      if (parts.length == 3) {
        return DateTime(int.parse(parts[2]), int.parse(parts[1]), int.parse(parts[0]));
      }
    } else if (dateStr.contains('-')) {
      // Format: yyyy-MM-dd
      return DateTime.parse(dateStr);
    } else if (dateStr.contains('déc.')) {
      // Format: dd déc. yyyy
      List<String> parts = dateStr.split(' ');
      if (parts.length == 3) {
        int day = int.parse(parts[0]);
        int month = 12; // December
        int year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    } else if (dateStr.contains('nov.')) {
      // Format: dd nov. yyyy
      List<String> parts = dateStr.split(' ');
      if (parts.length == 3) {
        int day = int.parse(parts[0]);
        int month = 11; // November
        int year = int.parse(parts[2]);
        return DateTime(year, month, day);
      }
    }
    throw FormatException("Unable to parse date: $dateStr");
  }

  void _onDateFilterChanged(Map<String, String> dateRange) {
    setState(() {
      _startDateFilter = dateRange['startDate'];
      _endDateFilter = dateRange['endDate'];
    });
    // Apply the filter immediately
    _filterDossiers();
  }

  void _clearDateFilter() {
    setState(() {
      _startDateFilter = null;
      _endDateFilter = null;
    });
    // Reapply filters without date constraint
    _filterDossiers();
  }

  void onSearchTap() {
    setState(() {
      _isSearchBarVisible = !_isSearchBarVisible;
      if (!_isSearchBarVisible) {
        _searchAnimationController.reverse();
        _searchController.clear(); // Clear search when hidden
        _startDateFilter = null; // Clear date filters when hidden
        _endDateFilter = null;
        _filterDossiers(); // Reset filter
      } else {
        _searchAnimationController.forward();
      }
    });
  }
  

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
              isSearchBarVisible: _isSearchBarVisible,
              onSearchTap: onSearchTap,
              pageSection: HeaderEnum.dossiers, 
              title: "DOSSIERS ÉTUDIANT"),
          AnimatedBuilder(
            animation: _searchAnimationController,
            builder: (context, child) {
              return SliverPersistentHeader(
                delegate: SearchBarSliver(
                  extentHeight: _searchAnimationController.value * 60.0,
                  searchController: _searchController,
                  onSearchChanged: (query) => _filterDossiers(),
                  onDateFilterChanged: _onDateFilterChanged,
                  onClearDateFilter: _clearDateFilter,
                  hasActiveFilter: _startDateFilter != null && _endDateFilter != null,
                  hintText: "Rechercher un dossier...",
                ),
              );
            },
          ),
          _isLoading
          ? SliverFillRemaining(
              child: Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              ),
            )
          : SliverFillRemaining(
              child: _filteredDossiers.isEmpty? 
                 Center(child: EmptyMessage(message: "Aucun dossier trouvé"))
               : ListView.builder(
                itemCount: _filteredDossiers.length,
                itemBuilder: (context, index) {
                  final dossier = _filteredDossiers[index];
                  return DossierItem(dossier: dossier);
                }
              )
            ) 
        ],
      ),
    );
  }
}
