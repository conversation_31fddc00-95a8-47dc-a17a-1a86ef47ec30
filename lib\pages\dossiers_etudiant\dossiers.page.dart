import 'package:flutter/material.dart';
import 'package:Kairos/enums/header_enums.dart';
import 'package:Kairos/widgets/custom_app_bar.widget.dart';
import 'package:Kairos/models/dossier.dart';
import 'package:Kairos/pages/dossiers_etudiant/dossier_item.widget.dart';
import 'package:Kairos/widgets/custom_spinner.dart';
import 'package:Kairos/widgets/empty_message.widget.dart';
import 'package:Kairos/widgets/search_bar_sliver.widget.dart';

class DossiersPage extends StatefulWidget {
  const DossiersPage({super.key});
  @override
  State<DossiersPage> createState() => _DossiersPageState();
}

class _DossiersPageState extends State<DossiersPage> {
  bool _isLoading = true;
  bool _isSearchBarVisible = false; // State to control search bar visibility
  final List<Dossier> dossiers = [
    Dossier(
      initials: 'EI',
      title: 'Entretien individuel',
      boldTitle: 'ENTRETIEN INDIVIDUEL',
      description: 'Entretien individuel avec le responsable de la scolarité',
      date: '14 déc. 2022',
    ),
    <PERSON><PERSON><PERSON>(
      initials: 'RÉ',
      title: 'Réclamation',
      boldTitle: 'RECLAMATION',
      description: 'L\'article Code du travail vous accorde un droit de réclamation',
      date: '13 déc. 2022',
    ),
    Dossier(
      initials: 'DM',
      title: 'Dossier médical',
      boldTitle: 'DOSSIER MEDICAL',
      description: 'Les informations médicales portant sur le patient',
      date: '23 nov. 2022',
    ),
    Dossier(
      initials: 'RD',
      title: 'Remise de documents',
      boldTitle: 'REMISE DOC ADMINISTRATIVE',
      description: 'Remise Doc Administrative',
      date: '22 nov. 2022',
    ),
    Dossier(
      initials: 'RR',
      title: 'Renvoi pour absence répétitive',
      boldTitle: 'ABSENCE RÉPÉTITIVE',
      description: 'Absence Tst',
      date: '22 nov. 2022',
    ),
  ];
  late List<Dossier> _filteredDossiers = []; // List to hold filtered dossiers
  late TextEditingController _searchController; // Controller for the search bar


  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    _filteredDossiers = List.from(dossiers); // Initialize filtered list with all dossiers
    _searchController.addListener(_filterDossiers);
    Future.delayed(const Duration(seconds: 1), () {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    });
  }


  void _filterDossiers(){
      final query = _searchController.text.toLowerCase();
      setState(() {
        _filteredDossiers = dossiers.where((dossier) {
          // Filter logic: check if any relevant field contains the query
          return dossier.title.toLowerCase().contains(query) ||
                 dossier.description.toLowerCase().contains(query) ||
                 dossier.date.toLowerCase().contains(query);
        }).toList();
      });
    }


    void onSearchTap() {
      setState(() {
        _isSearchBarVisible = !_isSearchBarVisible;
        if (!_isSearchBarVisible) {
          _searchController.clear(); // Clear search when hidden
          _filterDossiers(); // Reset filter
        }
      });
    }
  

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          CustomAppBar(
              isSearchBarVisible: _isSearchBarVisible,
              onSearchTap: onSearchTap,
              pageSection: HeaderEnum.dossiers, 
              title: "DOSSIERS ÉTUDIANT"),
          SliverPersistentHeader(
            pinned: true,
            delegate: SearchBarSliver(
              extentHeight: _isSearchBarVisible ? 60.0 : 0.0,
              searchController: _searchController,
              onSearchChanged: (value) => _filterDossiers(),
            ),
          ),
          _isLoading
          ? SliverFillRemaining(
              child: Center(
                child: CustomSpinner(
                  size: 60.0,
                  strokeWidth: 5.0,
                ),
              ),
            )
          : SliverFillRemaining(
              child: _filteredDossiers.isEmpty? 
                 Center(child: EmptyMessage(message: "Aucun dossier trouvé"))
               : ListView.builder(
                itemCount: _filteredDossiers.length,
                itemBuilder: (context, index) {
                  final dossier = _filteredDossiers[index];
                  return DossierItem(dossier: dossier);
                }
              )
            ) 
        ],
      ),
    );
  }
}
