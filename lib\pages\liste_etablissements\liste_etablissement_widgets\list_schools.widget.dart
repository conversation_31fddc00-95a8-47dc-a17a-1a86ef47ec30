


import 'package:flutter/material.dart';

class ListSchoolsWidget extends StatelessWidget{
  const ListSchoolsWidget({super.key});

  @override
  Widget build(BuildContext context){
    
        return  
            ListView.builder(
              shrinkWrap: true,
              itemCount:9,
              itemBuilder: (context, index){
                return Card(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  child: ListTile(
                    title: Padding(
                      padding: const EdgeInsets.symmetric(vertical: 1.0),
                      child: ListTile(
                        dense: true,
                        title: Text("IAM $index"),
                        subtitle: Text.rich(TextSpan(children: [
                          TextSpan(text: "PROFILE: ", style: TextStyle(fontWeight: FontWeight.bold)),
                          TextSpan(text: "Étudiant "),

                          TextSpan(text: "ID: ", style: TextStyle(fontWeight: FontWeight.bold)),
                          TextSpan(text: "1234"),
                        ])),
                        // subtitle: Text(" ÉTUDIANT ID: 1234 ", style: TextStyle(fontSize: 12))
                        ),
                    ),
                    leading: Image.asset("assets/images/logo_iam.png"),
                    trailing: Icon(Icons.arrow_forward_ios, size: 10,),
                    onTap: () {
                      debugPrint('the user clicked on `Etablissement $index`');
                      Navigator.pushNamed(context, "/dashboard");
                    },
                  ),
                );
              },
          );
      
  }
}